"""
Independent Report Generator

This module generates reports without dependency on BIRT files, using the new
report definition system and enhanced formatting engine.
"""

import pandas as pd
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import calendar

from database_manager import DatabaseManager
from report_definitions import REPORT_REGISTRY
from report_formatter import REPORT_FORMATTER

logger = logging.getLogger(__name__)

class IndependentReportGenerator:
    """Independent report generation engine"""
    
    def __init__(self, config_path: str = 'config.yaml'):
        """
        Initialize the independent report generator
        
        Args:
            config_path: Path to configuration file
        """
        self.db_manager = DatabaseManager(config_path)
        self.registry = REPORT_REGISTRY
        self.formatter = REPORT_FORMATTER
        self.config = self.db_manager.config
        
        # Create output directory if it doesn't exist
        self.output_dir = Path(self.config.get('application', {}).get('output_directory', './output'))
        self.output_dir.mkdir(exist_ok=True)
        
        logger.info(f"Initialized independent report generator with {len(self.registry.get_report_names())} reports")
    
    def generate_filename(self, report_name: str, year: int, month: int) -> str:
        """
        Generate output filename for a report
        
        Args:
            report_name: Name of the report
            year: Year parameter
            month: Month parameter
            
        Returns:
            Generated filename
        """
        # Convert month number to 3-letter abbreviation
        month_abbr = calendar.month_abbr[month].upper()
        date_str = f"{month_abbr}{year}"
        
        # Use standard naming pattern
        filename = f"{report_name}_{date_str}.xlsx"
        
        return filename
    
    def prepare_query_parameters(self, report_def, year: int, month: int) -> List:
        """
        Prepare query parameters based on the report definition
        
        Args:
            report_def: Report definition
            year: Year parameter
            month: Month parameter
            
        Returns:
            List of parameters in correct order
        """
        parameters = []
        
        for param_name in report_def.parameter_order:
            if param_name == 'year':
                parameters.append(year)
            elif param_name == 'month':
                parameters.append(month)
        
        return parameters
    
    def save_to_excel(self, df: pd.DataFrame, filepath: str, report_def, year: int = None, month: int = None) -> bool:
        """
        Save DataFrame to Excel file with enhanced formatting

        Args:
            df: DataFrame to save
            filepath: Output file path
            report_def: Report definition for formatting
            year: Report year (for dynamic formatting)
            month: Report month (for dynamic formatting)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            # Get formatting options
            formatting = self.formatter.get_excel_formatting_options(report_def)
            
            # Write to Excel with enhanced formatting
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(
                    writer,
                    sheet_name=formatting['sheet_name'],
                    index=formatting['index']
                )

                # Get the workbook and worksheet for additional formatting
                workbook = writer.book
                worksheet = writer.sheets[formatting['sheet_name']]

                # Apply report-specific formatting
                if report_def.name == 'System Performance (Response Time)':
                    self._apply_system_performance_formatting(worksheet, df, year, month)
                else:
                    # Apply standard formatting for other reports
                    self._apply_standard_formatting(worksheet, df, formatting)
            
            logger.info(f"Report saved successfully: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save Excel file {filepath}: {str(e)}")
            return False

    def _apply_system_performance_formatting(self, worksheet, df, year: int = None, month: int = None):
        """Apply specific formatting for System Performance (Response Time) report"""
        from openpyxl.styles import PatternFill, Font, Alignment
        import calendar

        # Define colors to match example
        dark_gray_fill = PatternFill(start_color="FF2C2C2C", end_color="FF2C2C2C", fill_type="solid")
        light_green_fill = PatternFill(start_color="FFB8ED83", end_color="FFB8ED83", fill_type="solid")
        light_gray_fill = PatternFill(start_color="FFE2E2E2", end_color="FFE2E2E2", fill_type="solid")

        white_font = Font(color="FFFFFFFF", bold=True)
        dark_green_font = Font(color="FF004000", bold=True)
        black_font_bold = Font(color="FF000000", bold=True)

        # Header row formatting (row 1)
        # Cell A1: "System Performance" with dark gray background and white text
        worksheet['A1'].fill = dark_gray_fill
        worksheet['A1'].font = white_font

        # Generate dynamic month name in English
        if year and month:
            month_name = calendar.month_name[month].upper()
            date_header = f"{month_name} {year}"
        else:
            date_header = "JANUARY 2025"  # Fallback

        # Set the value first, then merge cells
        worksheet['B1'].value = date_header
        worksheet.merge_cells('B1:E1')
        worksheet['B1'].fill = dark_gray_fill
        worksheet['B1'].font = white_font
        worksheet['B1'].alignment = Alignment(horizontal='center')

        # Column headers row (row 2)
        # A2: "Transaksi (16)" with dark gray background
        worksheet['A2'].fill = dark_gray_fill
        worksheet['A2'].font = white_font

        # B2-E2: Column headers with light green background and dark green text
        for col in ['B2', 'C2', 'D2', 'E2']:
            worksheet[col].fill = light_green_fill
            worksheet[col].font = dark_green_font

        # Summary row formatting (last row)
        last_row = worksheet.max_row

        # Merge cells A(last):D(last) for "Non Compliance %"
        worksheet.merge_cells(f'A{last_row}:D{last_row}')
        worksheet[f'A{last_row}'].value = "Non Compliance %"
        worksheet[f'A{last_row}'].fill = light_gray_fill
        worksheet[f'A{last_row}'].font = black_font_bold
        worksheet[f'A{last_row}'].alignment = Alignment(horizontal='left')

        # E(last): Summary value with same formatting
        worksheet[f'E{last_row}'].fill = light_gray_fill
        worksheet[f'E{last_row}'].font = black_font_bold

        # Apply borders to all cells
        from openpyxl.styles import Border, Side
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        for row_idx in range(1, last_row + 1):
            for col_idx in range(1, 6):  # A to E columns
                cell = worksheet.cell(row=row_idx, column=col_idx)
                cell.border = thin_border

        # Apply number formatting to Exceed % column (column E)
        for row_idx in range(3, last_row + 1):  # Skip header rows
            cell = worksheet.cell(row=row_idx, column=5)  # Column E
            cell.number_format = '0.00'

        # Set column widths to match example
        worksheet.column_dimensions['A'].width = 45
        worksheet.column_dimensions['B'].width = 12
        worksheet.column_dimensions['C'].width = 12
        worksheet.column_dimensions['D'].width = 12
        worksheet.column_dimensions['E'].width = 12

    def _apply_standard_formatting(self, worksheet, df, formatting):
        """Apply enhanced standard formatting for other reports"""
        from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
        from openpyxl.styles.numbers import FORMAT_GENERAL, FORMAT_DATE_DATETIME, FORMAT_PERCENTAGE_00

        # Define standard styling
        header_fill = PatternFill(start_color="FF242424", end_color="FF242424", fill_type="solid")
        header_font = Font(color="FFFFFFFF", bold=True)
        header_alignment = Alignment(horizontal='center', vertical='center')

        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Apply header formatting (first row)
        if len(df) > 0:
            for col_idx, col_name in enumerate(df.columns, 1):
                cell = worksheet.cell(row=1, column=col_idx)
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = thin_border

        # Apply borders to all data cells
        for row_idx in range(1, len(df) + 2):  # +2 because Excel is 1-indexed and includes header
            for col_idx in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=row_idx, column=col_idx)
                cell.border = thin_border

        # Apply number formatting based on column definitions
        for col_name, number_format in formatting.get('number_formats', {}).items():
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = chr(64 + col_idx)

                # Apply number format to the entire column (excluding header)
                for row_idx in range(2, len(df) + 2):
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    if number_format == '0.00':
                        cell.number_format = '0.00'
                    elif 'datetime' in number_format.lower():
                        cell.number_format = 'yyyy-mm-dd hh:mm:ss'

        # Apply column widths
        for col_name, width in formatting['column_widths'].items():
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1  # Excel is 1-indexed
                col_letter = chr(64 + col_idx)  # Convert to letter
                worksheet.column_dimensions[col_letter].width = width

        # Auto-adjust column widths for columns without specific width
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter

            # Skip if we already set a specific width
            col_name = df.columns[ord(column_letter) - 65] if ord(column_letter) - 65 < len(df.columns) else None
            if col_name and col_name in formatting['column_widths']:
                continue

            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def generate_single_report(self, report_name: str, year: int, month: int, 
                             progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        Generate a single report using the independent system
        
        Args:
            report_name: Name of the report to generate
            year: Year parameter
            month: Month parameter
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Dictionary with generation results
        """
        result = {
            'report_name': report_name,
            'success': False,
            'filename': '',
            'rows_generated': 0,
            'error_message': '',
            'execution_time': 0
        }
        
        start_time = datetime.now()
        
        try:
            if progress_callback:
                progress_callback(f"Starting {report_name}...")
            
            # Get report definition
            report_def = self.registry.get_report(report_name)
            if not report_def:
                raise ValueError(f"Report definition not found: {report_name}")
            
            # Load SQL query
            query = self.registry.load_sql_query(report_def.sql_file)
            if not query:
                raise ValueError(f"SQL query not found for report: {report_name}")
            
            if progress_callback:
                progress_callback(f"Executing query for {report_name}...")
            
            # Prepare parameters and execute query
            parameters = self.prepare_query_parameters(report_def, year, month)
            
            logger.info(f"Executing {report_name} on {report_def.database} with parameters: {parameters}")
            df = self.db_manager.execute_query(report_def.database, query, parameters)
            
            if progress_callback:
                progress_callback(f"Formatting {report_name}...")
            
            # Apply enhanced formatting
            formatted_df = self.formatter.format_report(df, report_name, year, month)
            
            if progress_callback:
                progress_callback(f"Saving {report_name} to Excel...")
            
            # Generate filename and save
            filename = self.generate_filename(report_name, year, month)
            filepath = self.output_dir / filename
            
            success = self.save_to_excel(formatted_df, str(filepath), report_def, year, month)
            
            if success:
                result.update({
                    'success': True,
                    'filename': filename,
                    'rows_generated': len(formatted_df),
                    'execution_time': (datetime.now() - start_time).total_seconds()
                })
                
                if progress_callback:
                    progress_callback(f"✓ {report_name} completed ({len(formatted_df)} rows)")
            else:
                result['error_message'] = "Failed to save Excel file"
                
        except Exception as e:
            error_msg = str(e)
            result['error_message'] = error_msg
            logger.error(f"Failed to generate {report_name}: {error_msg}")
            
            if progress_callback:
                progress_callback(f"✗ {report_name} failed: {error_msg}")
        
        result['execution_time'] = (datetime.now() - start_time).total_seconds()
        return result
    
    def generate_multiple_reports(self, report_names: List[str], year: int, month: int,
                                progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        Generate multiple reports
        
        Args:
            report_names: List of report names to generate
            year: Year parameter
            month: Month parameter
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Dictionary with overall results
        """
        overall_result = {
            'total_reports': len(report_names),
            'successful_reports': 0,
            'failed_reports': 0,
            'results': [],
            'total_execution_time': 0
        }
        
        start_time = datetime.now()
        
        for i, report_name in enumerate(report_names, 1):
            if progress_callback:
                progress_callback(f"Processing report {i}/{len(report_names)}: {report_name}")
            
            result = self.generate_single_report(report_name, year, month, progress_callback)
            overall_result['results'].append(result)
            
            if result['success']:
                overall_result['successful_reports'] += 1
            else:
                overall_result['failed_reports'] += 1
        
        overall_result['total_execution_time'] = (datetime.now() - start_time).total_seconds()
        
        if progress_callback:
            progress_callback(f"Completed: {overall_result['successful_reports']} successful, "
                            f"{overall_result['failed_reports']} failed")
        
        return overall_result
    
    def get_available_reports(self) -> List[str]:
        """
        Get list of available report names
        
        Returns:
            List of report names
        """
        return self.registry.get_report_names()
    
    def validate_report_parameters(self, report_name: str, year: int, month: int) -> Tuple[bool, str]:
        """
        Validate report parameters
        
        Args:
            report_name: Name of the report
            year: Year parameter
            month: Month parameter
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check if report exists
        if not self.registry.get_report(report_name):
            return False, f"Report '{report_name}' not found"
        
        # Validate year
        current_year = datetime.now().year
        if year < 2000 or year > current_year + 1:
            return False, f"Invalid year: {year}. Must be between 2000 and {current_year + 1}"
        
        # Validate month
        if month < 1 or month > 12:
            return False, f"Invalid month: {month}. Must be between 1 and 12"
        
        return True, ""


def main():
    """Test the independent report generator"""
    logging.basicConfig(level=logging.INFO)
    
    generator = IndependentReportGenerator()
    
    print("Available reports:")
    for report_name in generator.get_available_reports():
        print(f"  - {report_name}")
    
    # Test parameter validation
    print("\nTesting parameter validation:")
    valid, msg = generator.validate_report_parameters("Service Request", 2025, 1)
    print(f"Service Request, 2025, 1: {'Valid' if valid else f'Invalid - {msg}'}")
    
    # Test with a single report
    print("\nTesting single report generation:")
    try:
        def progress_callback(message):
            print(f"  {message}")
        
        result = generator.generate_single_report("Customer Service Performance (Telephone)", 2024, 1, progress_callback)
        print(f"Result: {result}")
    except Exception as e:
        print(f"Test failed: {e}")


if __name__ == "__main__":
    main()
