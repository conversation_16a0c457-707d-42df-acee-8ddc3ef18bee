"""
Independent Report System - Report Definitions

This module defines all report configurations without dependency on BIRT files.
Each report has its metadata, column mappings, formatting rules, and processing requirements.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class ColumnDefinition:
    """Definition for a report column"""
    name: str
    data_type: str
    format_rule: Optional[str] = None
    width: Optional[int] = None
    alignment: str = 'left'

@dataclass
class ReportDefinition:
    """Complete definition for a report"""
    name: str
    description: str
    database: str
    sql_file: str
    columns: List[ColumnDefinition]
    parameter_count: int
    parameter_order: List[str]
    special_processing: List[str]
    sheet_name: str = 'Sheet0'
    
class ReportRegistry:
    """Registry of all available reports"""
    
    def __init__(self):
        self.reports = self._initialize_reports()
    
    def _initialize_reports(self) -> Dict[str, ReportDefinition]:
        """Initialize all report definitions"""
        
        reports = {}
        
        # Customer Service Performance (Multiple Channel)
        reports['Customer Service Performance (Multiple Channel)'] = ReportDefinition(
            name='Customer Service Performance (Multiple Channel)',
            description='Customer service performance across multiple channels',
            database='cdc_poms',
            sql_file='customer_service_multiple_channel.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Channel', 'string', width=15),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Second)', 'integer', width=20),
                ColumnDefinition('Actual SLA (Second)', 'integer', width=18)
            ],
            parameter_count=2,
            parameter_order=['year', 'month'],
            special_processing=[]
        )
        
        # Customer Service Performance (Telephone)
        reports['Customer Service Performance (Telephone)'] = ReportDefinition(
            name='Customer Service Performance (Telephone)',
            description='Customer service performance for telephone channel with MTD summary',
            database='cdc_poms',
            sql_file='customer_service_telephone.sql',
            columns=[
                ColumnDefinition('DATE CALL', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=18),
                ColumnDefinition('CALL OFFER', 'integer', width=12),
                ColumnDefinition('CALL HANDLE', 'integer', width=12),
                ColumnDefinition('CALL WITHIN 10', 'integer', width=15),
                ColumnDefinition('ABANDON SHORT', 'integer', width=15),
                ColumnDefinition('ABANDON LONG', 'integer', width=15),
                ColumnDefinition('ABANDON %', 'float', '0.00', width=12),
                ColumnDefinition('ANSWER %', 'float', '0.00', width=12),
                ColumnDefinition('SLA %', 'float', '0.00', width=12)
            ],
            parameter_count=2,
            parameter_order=['year', 'month'],
            special_processing=['add_mtd_row', 'rename_service_level_column']
        )
        
        # Incident Management (Case Acknowledgement)
        reports['Incident Management (Case Acknowledgement)'] = ReportDefinition(
            name='Incident Management (Case Acknowledgement)',
            description='Incident management case acknowledgement tracking',
            database='cdc_poms',
            sql_file='incident_management_case_acknowledgement.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Case Name', 'string', width=50),
                ColumnDefinition('Task Number', 'string', width=15),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Second)', 'integer', width=20),
                ColumnDefinition('Actual SLA (Second)', 'integer', width=18)
            ],
            parameter_count=4,
            parameter_order=['year', 'month', 'year', 'month'],
            special_processing=[]
        )
        
        # Incident Management (RIT)
        reports['Incident Management (RIT)'] = ReportDefinition(
            name='Incident Management (RIT)',
            description='Incident management RIT tracking',
            database='cdc_poms',
            sql_file='incident_management_rit.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Case Name', 'string', width=50),
                ColumnDefinition('Task Number', 'string', width=15),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Second)', 'integer', width=20),
                ColumnDefinition('Actual SLA (Second)', 'integer', width=18)
            ],
            parameter_count=4,
            parameter_order=['year', 'month', 'year', 'month'],
            special_processing=[]
        )
        
        # Incident Management S123
        reports['Incident Management S123'] = ReportDefinition(
            name='Incident Management S123',
            description='Incident management S123 severity tracking',
            database='cdc_poms',
            sql_file='incident_management_s123.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Case Name', 'string', width=50),
                ColumnDefinition('Task Number', 'string', width=15),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Day)', 'integer', width=18),
                ColumnDefinition('Actual SLA (Day)', 'integer', width=16)
            ],
            parameter_count=6,
            parameter_order=['year', 'month', 'year', 'month', 'year', 'month'],
            special_processing=[]
        )
        
        # Incident Management S4
        reports['Incident Management S4'] = ReportDefinition(
            name='Incident Management S4',
            description='Incident management S4 severity tracking',
            database='cdc_poms',
            sql_file='incident_management_s4.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Redmine No.', 'string', width=12),
                ColumnDefinition('Case Name        ', 'string', width=50),  # Note: exact spacing from example
                ColumnDefinition('Task Name', 'string', width=50),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Duration', 'string', width=20),
                ColumnDefinition('Available SLA (Day)', 'integer', width=18),
                ColumnDefinition('Actual SLA (Day)', 'integer', width=16),
                ColumnDefinition('Implementation Issue?', 'string', width=20),
                ColumnDefinition('Exceed SLA (Day)', 'integer', width=16)
            ],
            parameter_count=5,
            parameter_order=['year', 'month', 'year', 'month', 'year'],
            special_processing=[]
        )
        
        # Service Request
        reports['Service Request'] = ReportDefinition(
            name='Service Request',
            description='Service request tracking',
            database='cdc_poms',
            sql_file='service_request.sql',
            columns=[
                ColumnDefinition('TICKET NUMBER', 'string', width=15),
                ColumnDefinition('CATEGORY', 'string', width=20),
                ColumnDefinition('CREATED DATE', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('REQUEST SUBJECT', 'string', width=50),
                ColumnDefinition('REASON FOR REQUEST', 'string', width=25),
                ColumnDefinition('EXPECTED START', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=18),
                ColumnDefinition('EXPECTED COMPLETED', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('EXPECTED DURATION', 'integer', width=18),
                ColumnDefinition('ACTUAL START', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=18),
                ColumnDefinition('ACTUAL COMPLETED', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=18),
                ColumnDefinition('ACTUAL DURATION', 'integer', width=16),
                ColumnDefinition('EXCEED DURATION', 'integer', width=16)
            ],
            parameter_count=2,
            parameter_order=['year', 'month'],
            special_processing=[]
        )
        
        # System Availability (Infra)
        reports['System Availability (Infra)'] = ReportDefinition(
            name='System Availability (Infra)',
            description='System availability infrastructure monitoring',
            database='cdc_poms',
            sql_file='system_availability.sql',
            columns=[
                ColumnDefinition('HOST GROUP', 'string', width=15),
                ColumnDefinition('DOWNTIME', 'string', width=20),
                ColumnDefinition('COUNTER', 'integer', width=12),
                ColumnDefinition('ALL DOWN FLAG', 'string', width=15)
            ],
            parameter_count=2,
            parameter_order=['month', 'year'],
            special_processing=[]
        )
        
        # System Performance (Response Time)
        reports['System Performance (Response Time)'] = ReportDefinition(
            name='System Performance (Response Time)',
            description='System performance response time monitoring with 16 transactions',
            database='cdc_poms',
            sql_file='system_performance.sql',
            columns=[
                ColumnDefinition('System Performance', 'string', width=35),
                ColumnDefinition('Januari 2025', 'string', width=15),  # This will be dynamic based on month/year
                ColumnDefinition('Unnamed: 2', 'string', width=12),
                ColumnDefinition('Unnamed: 3', 'string', width=12),
                ColumnDefinition('Unnamed: 4', 'string', width=12)
            ],
            parameter_count=30,  # 15 UNION blocks * 2 parameters each
            parameter_order=['month', 'year'] * 15,
            special_processing=['add_header_row', 'add_non_compliance_summary']
        )
        
        return reports
    
    def get_report(self, name: str) -> Optional[ReportDefinition]:
        """Get a report definition by name"""
        return self.reports.get(name)
    
    def get_all_reports(self) -> Dict[str, ReportDefinition]:
        """Get all report definitions"""
        return self.reports
    
    def get_report_names(self) -> List[str]:
        """Get list of all report names"""
        return list(self.reports.keys())
    
    def load_sql_query(self, sql_file: str) -> str:
        """Load SQL query from file"""
        sql_path = Path('queries') / sql_file
        if sql_path.exists():
            with open(sql_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # Extract just the SQL query (remove comments)
                lines = content.split('\n')
                sql_lines = []
                for line in lines:
                    if not line.strip().startswith('--') and line.strip():
                        sql_lines.append(line)
                return '\n'.join(sql_lines).strip().rstrip(';')
        return ""


# Global registry instance
REPORT_REGISTRY = ReportRegistry()
